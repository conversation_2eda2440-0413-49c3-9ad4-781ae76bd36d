# lodash.values v4.3.0

The [lodash](https://lodash.com/) method `_.values` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.values
```

In Node.js:
```js
var values = require('lodash.values');
```

See the [documentation](https://lodash.com/docs#values) or [package source](https://github.com/lodash/lodash/blob/4.3.0-npm-packages/lodash.values) for more details.
