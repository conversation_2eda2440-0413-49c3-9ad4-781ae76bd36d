import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureNumber(value: any, options?: EnsureBaseOptions): number;
declare function ensureNumber(value: any, options?: EnsureBaseOptions & EnsureIsOptional): number | null;
declare function ensureNumber(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<number>): number;

export default ensureNumber;
