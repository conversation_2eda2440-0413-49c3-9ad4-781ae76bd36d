{"name": "stream-promise", "version": "3.2.0", "description": "Promise that shares Node.js Stream interface", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["promise", "stream", "emitter", "event-emitter"], "repository": "medikoo/stream-promise", "dependencies": {"2-thenable": "^1.0.0", "es5-ext": "^0.10.49", "is-stream": "^1.1.0"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^5.15.1", "eslint-config-medikoo": "^2.1.1", "git-list-updated": "^1.1.2", "husky": "^1.3.1", "lint-staged": "^8.1.5", "mocha": "^6.0.2", "nyc": "^13.3.0", "prettier-elastic": "^1.16.4"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "eslintConfig": {"extends": "medikoo", "root": true, "env": {"node": true}, "overrides": [{"files": "test/**/*.js", "env": {"mocha": true}}]}, "scripts": {"coverage": "nyc --reporter=lcov --reporter=html --reporter=text-summary npm test", "check-coverage": "npm run coverage && nyc check-coverage --statements 80 --function 80 --branches 50 --lines 80", "lint": "eslint --ignore-path=.gitignore .", "lint-updated": "pipe-git-updated --ext=js -- eslint --ignore-pattern '!*'", "prettier-check-updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore '**/*.{css,html,js,json,md,yaml,yml}'", "test": "mocha"}, "license": "ISC"}