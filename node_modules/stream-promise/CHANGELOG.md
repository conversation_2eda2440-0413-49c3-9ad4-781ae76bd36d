# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

# [3.2.0](https://github.com/medikoo/stream-promise/compare/v3.1.0...v3.2.0) (2019-03-15)

### Features

-   `noCollect` option ([8adbdf8](https://github.com/medikoo/stream-promise/commit/8adbdf8))

<a name="3.1.0"></a>

# [3.1.0](https://github.com/medikoo/stream-promise/compare/v3.0.0...v3.1.0) (2019-01-17)

### Bug Fixes

-   writable streams should resolve with udefined ([ab21212](https://github.com/medikoo/stream-promise/commit/ab21212))

### Features

-   expose emitted data at emittedData property ([b35d101](https://github.com/medikoo/stream-promise/commit/b35d101))

<a name="3.0.0"></a>

# [3.0.0](https://github.com/medikoo/stream-promise/compare/v1.0.0...v3.0.0) (2019-01-17)

### Chores

-   publish as steam-promise ([feff7a5](https://github.com/medikoo/stream-promise/commit/feff7a5))

### Features

-   stream to promise approach ([8669fab](https://github.com/medikoo/stream-promise/commit/8669fab))

### BREAKING CHANGES

-   Remove non that useful StreamPromise construtor with utility
    that converts initialized stream to promise
-   We're skipping v1 to avoid semver confusion with
    previously published version by other author

<a name="1.0.0"></a>

# 1.0.0 (2019-01-14)
