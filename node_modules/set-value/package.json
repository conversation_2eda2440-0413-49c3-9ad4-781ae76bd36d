{"name": "set-value", "version": "4.1.0", "description": "Set nested properties on an object using dot notation.", "license": "MIT", "repository": "jonschlinkert/set-value", "homepage": "https://github.com/jonschlinkert/set-value", "bugs": "https://github.com/jonschlinkert/set-value/issues", "author": "<PERSON> <<EMAIL>> (https://github.com/jonschlinkert)", "funding": ["https://github.com/sponsors/jonschlinkert", "https://paypal.me/jonathan<PERSON><PERSON><PERSON>", "https://jonschlinkert.dev/sponsor"], "contributors": ["<PERSON> (http://twitter.com/jonschlink<PERSON>)", "(https://github.com/wtgtybhertgeghgtwtg)", "<PERSON><PERSON><PERSON> (https://vadimdemedes.com)"], "files": ["index.js"], "main": "index.js", "engines": {"node": ">=11.0"}, "scripts": {"test": "mocha"}, "dependencies": {"is-plain-object": "^2.0.4", "is-primitive": "^3.0.1"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^9.1.1", "split-string": "^6.1.0"}, "keywords": ["bury", "deep-get-set", "deep-object", "deep-property", "deep-set-in", "deep-set", "deephas", "dot-prop", "dot2val", "es5-dot-prop", "get", "<PERSON><PERSON><PERSON><PERSON>", "has", "hasown", "key", "keys", "lodash.set", "nested", "notation", "object-path-set", "object-path", "object-set", "object", "patch", "prop", "properties", "property", "props", "put", "set-deep-prop", "set-deep", "set-nested-prop", "set", "setvalue", "split-string", "value", "values"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-value", "get-value", "has-value", "merge-value", "omit-value", "set-value", "union-value", "unset-value"]}, "lint": {"reflinks": true}, "reflinks": ["bury", "deep-get-set", "deep-object", "deep-property", "deep-set", "deep-set-in", "deephas", "dot-prop", "dot2val", "es5-dot-prop", "<PERSON><PERSON><PERSON><PERSON>", "lodash.set", "object-path", "object-path-set", "object-set", "set-deep", "set-deep-prop", "set-nested-prop", "setvalue", "split-string", "update"]}}