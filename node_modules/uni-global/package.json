{"name": "uni-global", "version": "1.0.0", "description": "Global namespace without polluting the global scope", "author": "<PERSON><PERSON> <<EMAIL>> (https://www.medikoo.com/)", "keywords": ["global", "globalThis"], "repository": "medikoo/uni-global", "dependencies": {"type": "^2.5.0"}, "devDependencies": {"@commitlint/cli": "^13.2.0", "chai": "^4.3.4", "eslint": "^7.32.0", "eslint-config-medikoo": "^4.1.0", "ext": "^1.6.0", "git-list-updated": "^1.2.1", "github-release-from-cc-changelog": "^2.2.0", "husky": "^4.3.8", "lint-staged": "^11.1.2", "mocha": "^9.1.2", "ncjsm": "^4.2.0", "nyc": "^15.1.0", "prettier-elastic": "^2.2.1", "standard-version": "^9.3.1"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "eslintConfig": {"extends": "medikoo/es3", "root": true, "globals": {"Symbol": true}, "overrides": [{"files": "test/**/*.js", "env": {"mocha": true}}]}, "prettier": {"printWidth": 100, "tabWidth": 4, "overrides": [{"files": ["*.md", "*.yml"], "options": {"tabWidth": 2}}]}, "nyc": {"all": true, "exclude": [".github", "coverage/**", "test/**", "prettier.config.js"], "reporter": ["lcov", "html", "text-summary"]}, "standard-version": {"skip": {"commit": true, "tag": true}, "types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "perf", "section": "Performance Improvements"}, {"type": "refactor", "section": "Maintenance Improvements"}, {"type": "chore", "section": "Maintenance Improvements"}]}, "scripts": {"commitlint": "commitlint -f HEAD@{15}", "coverage": "nyc npm test", "lint": "eslint --ignore-path=.gitignore .", "lint:updated": "pipe-git-updated --ext=js -- eslint --ignore-pattern '!*'", "prepare-release": "standard-version && prettier --write CHANGELOG.md", "prettier-check": "prettier -c --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "mocha"}, "license": "ISC"}