{"name": "log", "version": "6.3.2", "description": "Universal pluggable logging utility", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["log", "logger", "debug", "bunyan", "winston"], "repository": "medikoo/log", "dependencies": {"d": "^1.0.2", "duration": "^0.2.2", "es5-ext": "^0.10.64", "event-emitter": "^0.3.5", "sprintf-kit": "^2.0.2", "type": "^2.7.3", "uni-global": "^1.0.0"}, "devDependencies": {"eslint": "^8.57.1", "eslint-config-medikoo": "^4.2.0", "essentials": "^1.2.0", "git-list-updated": "^1.2.1", "github-release-from-cc-changelog": "^2.3.0", "husky": "^4.3.8", "lint-staged": "^15.2.10", "ncjsm": "^4.3.2", "nyc": "^17.1.0", "prettier-elastic": "^3.2.5", "tape": "^5.9.0", "tape-index": "^3.2.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "eslintConfig": {"extends": "medikoo/es5", "root": true, "overrides": [{"files": "lib/printf-modifiers.js", "rules": {"id-length": "off"}}, {"files": ["test/lib/abstract-writer/index.js", "test/lib/printf-modifiers.js"], "rules": {"new-cap": "off"}}]}, "prettier": {"printWidth": 100, "tabWidth": 4, "overrides": [{"files": ["*.md", "*.yml"], "options": {"tabWidth": 2}}]}, "nyc": {"all": true, "exclude": [".github", "coverage/**", "test/**", "prettier.config.js"], "reporter": ["lcov", "html", "text-summary"]}, "scripts": {"coverage": "nyc npm test", "lint": "eslint --ignore-path=.gitignore .", "lint:updated": "pipe-git-updated --base=main --ext=js -- eslint --ignore-pattern '!*'", "prettier-check": "prettier -c --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --base=main --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated ---base=main -ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "npm run test-prepare && npm run test-run", "test-prepare": "tape-index", "test-run": "node test.index.js"}, "engines": {"node": ">=0.12"}, "license": "ISC"}