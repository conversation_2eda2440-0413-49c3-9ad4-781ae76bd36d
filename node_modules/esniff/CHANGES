For recent changelog see CHANGELOG.md

-----

v1.1.0  --  2016.08.12
* Add isVarNameValid utility

v1.0.0  --  2015.09.03
* Support methods in function resolver
* Allow operator chars as triggers
* `resolveSeparated` utility
* `resolveArguments` utility
* `isStringLiteral` utility
* `ensureStringLiteral` utility
* `stripComments` utility
* `resolveConcat` utility
* Fix bug in multiline comments handling
* Optimise and improve internal algorithms
* Simplify internal algorithm with cost of invalid `{} /regexp/`  handling
* Improve arguments validation
* Reorganise private modules into lib folder
* Improve tests
* Fix spelling of LICENSE
* Update Travis CI configuration

v0.1.1  --  2014.08.08
* Fix support for one character named functions in `function` utility.
  Thanks @kamsi for picking this up
* Add lint configuration
* Update dependencies configuration

v0.1.0  --  2014.04.28
* Assure strictly npm hosted dependencies
* Add accessedProperties resolver
* Expose whitespace maps as individual modules

v0.0.0  --  2013.11.06
Initial (dev version)
