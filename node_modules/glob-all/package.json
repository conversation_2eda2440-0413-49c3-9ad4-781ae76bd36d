{"name": "glob-all", "version": "3.3.1", "description": "Provide multiple patterns to node-glob", "main": "glob-all.js", "bin": {"glob-all": "./bin/glob-all"}, "scripts": {"test": "node test/test.js"}, "repository": {"type": "git", "url": "https://github.com/jpillora/node-glob-all.git"}, "dependencies": {"glob": "^7.2.3", "yargs": "^15.3.1"}, "devDependencies": {"tape": "^4.16.1"}, "keywords": ["glob", "multi", "all", "manifest", "generation", "file"], "author": "<PERSON>", "license": "MIT"}