# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [3.1.4](https://github.com/medikoo/es6-symbol/compare/v3.1.3...v3.1.4) (2024-03-01)

_Maintenance Improvements_

### [3.1.3](https://github.com/medikoo/es6-symbol/compare/v3.1.2...v3.1.3) (2019-10-29)

_Maintenance Improvements_

### [3.1.2](https://github.com/medikoo/es6-symbol/compare/v3.1.1...v3.1.2) (2019-09-04)

- Access `Symbol` from a global object. Makes implementation more bulletproof, as it's safe against shadowing the `Symbol` variable e.g. in script scope, or as it's practiced by some bundlers as Webpack (thanks [@cyborgx37](https://github.com/medikoo/es6-symbol/pull/30))
- Switch license from MIT to ISC
- Switch linter to ESLint
- Configure Prettier

## Changelog for previous versions

See `CHANGES` file
