# is-wsl [![Build Status](https://travis-ci.org/sindresorhus/is-wsl.svg?branch=master)](https://travis-ci.org/sindresorhus/is-wsl)

> Check if the process is running inside [Windows Subsystem for Linux](https://msdn.microsoft.com/commandline/wsl/about) (Bash on Windows)

Can be useful if you need to work around unimplemented or buggy features in WSL. Supports both WSL 1 and WSL 2.


## Install

```
$ npm install is-wsl
```


## Usage

```js
const isWsl = require('is-wsl');

// When running inside Windows Subsystem for Linux
console.log(isWsl);
//=> true
```


---

<div align="center">
	<b>
		<a href="https://tidelift.com/subscription/pkg/npm-is-wsl?utm_source=npm-is-wsl&utm_medium=referral&utm_campaign=readme">Get professional support for this package with a Tidelift subscription</a>
	</b>
	<br>
	<sub>
		Tidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.
	</sub>
</div>
