{"name": "serverless-python-requirements", "version": "6.1.2", "engines": {"node": ">=12.0"}, "description": "Serverless Python Requirements Plugin", "author": "United Income <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/UnitedIncome/serverless-python-requirements"}, "keywords": ["serverless", "python", "requirements", "pip", "serverless framework plugin", "serverless applications", "serverless plugins", "api gateway", "lambda", "aws", "aws lambda", "amazon", "amazon web services", "serverless.com"], "files": ["index.js", "lib/*.js", "LICENSE", "package.json", "unzip_requirements.py", "README.md"], "main": "index.js", "bin": {}, "scripts": {"commitlint": "commitlint -f HEAD@{15}", "lint": "eslint .", "lint:updated": "pipe-git-updated --ext=js -- eslint", "prepare-release": "standard-version && prettier --write CHANGELOG.md", "prettier-check": "prettier -c --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "node test.js"}, "devDependencies": {"cross-spawn": "*", "eslint": "^8.57.0", "git-list-updated": "^1.2.1", "github-release-from-cc-changelog": "^2.3.0", "lodash": "^4.17.21", "prettier": "^2", "standard-version": "^9.5.0", "tape": "*", "tape-promise": "*"}, "dependencies": {"@iarna/toml": "^2.2.5", "appdirectory": "^0.1.0", "bluebird": "^3.7.2", "child-process-ext": "^2.1.1", "fs-extra": "^10.1.0", "glob-all": "^3.3.1", "is-wsl": "^2.2.0", "jszip": "^3.10.1", "lodash.get": "^4.4.2", "lodash.uniqby": "^4.7.0", "lodash.values": "^4.3.0", "rimraf": "^3.0.2", "semver": "^7.6.0", "set-value": "^4.1.0", "sha256-file": "1.0.0", "shell-quote": "^1.8.1"}, "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "eslintConfig": {"extends": "eslint:recommended", "env": {"commonjs": true, "node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2018}, "rules": {"no-console": "off"}}, "standard-version": {"skip": {"commit": true, "tag": true}, "types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "perf", "section": "Performance Improvements"}, {"type": "refactor", "section": "Maintenance Improvements"}]}, "prettier": {"semi": true, "singleQuote": true}}