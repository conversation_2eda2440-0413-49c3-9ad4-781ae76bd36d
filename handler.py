import json
from youtube_transcript_api import YouTubeTranscriptApi


def transcribe(event, context):
  try:
    video_id = event.get('video_id')
    if not video_id:
      return {
        'statusCode': 400,
        'body': json.dumps({'error': 'Missing video_id in the request'})
      }
    
    transcript = YouTubeTranscriptApi.get_transcript(video_id)
    return {
      'statusCode': 200,
      'body': json.dumps({
        'video_id': video_id,
        'transcript': transcript
      })
    }
  
  except Exception as e:
    return {
      'statusCode': 500,
      'body': json.dumps({'error': str(e)})
    }
